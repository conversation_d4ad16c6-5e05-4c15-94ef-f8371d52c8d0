#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务调度器
"""

import schedule
import time
import logging
from datetime import datetime
from main import daily_data_collection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def scheduled_daily_collection():
    """定时执行每日数据采集"""
    logger.info("定时任务开始执行每日数据采集")
    try:
        success = daily_data_collection()
        if success:
            logger.info("定时任务执行成功")
        else:
            logger.error("定时任务执行失败")
    except Exception as e:
        logger.error(f"定时任务执行异常: {e}")

def main():
    """主函数"""
    logger.info("定时任务调度器启动")
    
    # 每天凌晨1点执行数据采集
    schedule.every().day.at("01:00").do(scheduled_daily_collection)
    
    # 也可以设置其他定时任务
    # schedule.every().hour.do(scheduled_daily_collection)  # 每小时执行
    # schedule.every(30).minutes.do(scheduled_daily_collection)  # 每30分钟执行
    
    logger.info("定时任务已设置：每天凌晨1点执行数据采集")
    
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("定时任务调度器停止")
            break
        except Exception as e:
            logger.error(f"定时任务调度器异常: {e}")
            time.sleep(60)

if __name__ == "__main__":
    main()
