@echo off
echo 安装污水处理平台数据采集工具...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 安装依赖
echo 正在安装Python依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 错误：依赖包安装失败
    pause
    exit /b 1
)

REM 创建导出目录
if not exist "exports" mkdir exports

echo.
echo 安装完成！
echo.
echo 使用说明：
echo 1. 编辑 config.json 文件，填入用户名和密码
echo 2. 运行 python main.py --mode test 测试连接
echo 3. 运行 python main.py --mode daily 执行每日数据采集
echo 4. 运行 python scheduler.py 启动定时任务
echo.
pause
