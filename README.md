# 农村生活污水智慧运维管控平台数据采集工具

这是一个用于自动采集污水处理平台数据的Python工具，支持定时采集、历史数据回补、数据导出等功能。

## 功能特性

- 🔐 **自动登录认证**：支持RSA加密登录
- 📊 **多种数据采集**：水电量数据、水质数据
- ⏰ **定时任务**：支持每日自动数据采集
- 💾 **数据存储**：SQLite数据库存储，支持数据去重
- 📈 **数据导出**：支持CSV格式导出
- 📝 **完整日志**：详细的操作日志和API调用记录
- 🔄 **历史数据**：支持指定时间范围的历史数据采集

## 系统要求

- Python 3.7+
- Windows/Linux/macOS

## 快速开始

### 1. 安装

**Windows:**
```bash
install.bat
```

**Linux/macOS:**
```bash
chmod +x install.sh
./install.sh
```

**手动安装:**
```bash
pip install -r requirements.txt
```

### 2. 配置

编辑 `config.json` 文件，填入您的账号信息：

```json
{
    "username": "您的用户名",
    "password": "您的密码",
    "base_url": "https://dyjswscl.sewage.huitucloud.cn:8088"
}
```

### 3. 测试连接

```bash
python main.py --mode test
```

### 4. 运行数据采集

**每日数据采集（获取昨天的数据）:**
```bash
python main.py --mode daily
```

**历史数据采集:**
```bash
python main.py --mode historical --start-date 2024-01-01 --end-date 2024-01-31
```

**启动定时任务（每天凌晨1点自动执行）:**
```bash
python scheduler.py
```

## 详细使用说明

### 命令行参数

```bash
python main.py [选项]

选项:
  --mode {daily,historical,export,test}
                        运行模式
  --start-date START_DATE
                        开始日期 (YYYY-MM-DD)
  --end-date END_DATE   结束日期 (YYYY-MM-DD)
  --table {water_electric_data,water_quality_data}
                        要导出的表名
  --output OUTPUT       输出文件名
```

### 使用示例

1. **测试连接:**
   ```bash
   python main.py --mode test
   ```

2. **每日数据采集:**
   ```bash
   python main.py --mode daily
   ```

3. **采集指定时间范围的历史数据:**
   ```bash
   python main.py --mode historical --start-date 2024-01-01 --end-date 2024-01-31
   ```

4. **导出水电量数据:**
   ```bash
   python main.py --mode export --table water_electric_data --output water_electric_2024.csv --start-date 2024-01-01 --end-date 2024-12-31
   ```

5. **导出水质数据:**
   ```bash
   python main.py --mode export --table water_quality_data --output water_quality_2024.csv --start-date 2024-01-01 --end-date 2024-12-31
   ```

## 数据采集频率建议

根据接口文档分析，建议的数据采集频率：

- **水电量日数据**: 每天采集一次（获取前一天数据）
- **水质数据**: 每天采集一次（实时监控数据）
- **站点基础数据**: 每周采集一次（变化较少）

## 文件结构

```
├── main.py                 # 主程序
├── sewage_platform_api.py  # API客户端类
├── scheduler.py            # 定时任务调度器
├── config.json            # 配置文件
├── requirements.txt       # Python依赖
├── install.bat           # Windows安装脚本
├── install.sh            # Linux/macOS安装脚本
├── README.md             # 说明文档
├── sewage_data.db        # SQLite数据库（运行后生成）
├── sewage_api.log        # API调用日志（运行后生成）
├── scheduler.log         # 定时任务日志（运行后生成）
└── exports/              # 数据导出目录
```

## 数据库表结构

### water_electric_data (水电量数据表)
- process_day_data_id: 主键ID
- station_id: 站点ID
- station_name: 站点名称
- region_code: 区域编码
- region_name: 区域名称
- processing_capacity: 处理量（m³）
- electricity_consumption: 用电量(kW·h)
- power_consumption: 吨水电耗量(kW·h)/m³
- report_time: 报告日期

### water_quality_data (水质数据表)
- station_id: 站点ID
- station_name: 站点名称
- test_time: 检测时间
- data_content: 数据内容（JSON格式）

### api_call_log (API调用日志表)
- api_name: 接口名称
- call_time: 调用时间
- status: 调用状态
- message: 返回消息
- data_count: 数据条数

## 定时任务设置

### 使用内置调度器
```bash
python scheduler.py
```

### 使用系统定时任务

**Linux/macOS (crontab):**
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天凌晨1点执行）
0 1 * * * cd /path/to/your/project && python3 main.py --mode daily
```

**Windows (任务计划程序):**
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为每天凌晨1点
4. 设置操作为启动程序：`python main.py --mode daily`

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认网络连接正常
   - 查看日志文件获取详细错误信息

2. **数据采集失败**
   - 检查Token是否过期（重新登录）
   - 确认接口地址是否正确
   - 查看API调用日志

3. **依赖安装失败**
   - 升级pip：`pip install --upgrade pip`
   - 使用国内镜像：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### 日志文件

- `sewage_api.log`: API调用和数据采集日志
- `scheduler.log`: 定时任务执行日志

## 注意事项

1. 请妥善保管账号密码，不要泄露给他人
2. 建议定期备份数据库文件
3. 监控日志文件，及时发现和处理异常
4. 根据实际需求调整数据采集频率

## 技术支持

如有问题，请查看日志文件或联系技术支持。
