#!/bin/bash

echo "安装污水处理平台数据采集工具..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 安装依赖
echo "正在安装Python依赖包..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "错误：依赖包安装失败"
    exit 1
fi

# 创建导出目录
mkdir -p exports

# 设置执行权限
chmod +x main.py
chmod +x scheduler.py

echo ""
echo "安装完成！"
echo ""
echo "使用说明："
echo "1. 编辑 config.json 文件，填入用户名和密码"
echo "2. 运行 python3 main.py --mode test 测试连接"
echo "3. 运行 python3 main.py --mode daily 执行每日数据采集"
echo "4. 运行 python3 scheduler.py 启动定时任务"
echo ""
