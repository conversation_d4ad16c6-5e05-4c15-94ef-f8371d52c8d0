1.项目说明
标题: 农村生活污水智慧运维管控平台接口文档
简介:接入平台接口前，需要向平台申请账号。使用用户名密码调用3.1.2接口获取登录Token，将Token设置在接口的请求Header中才能正常获取数据。
HOST: dyjswscl.sewage.huitucloud.cn:8088
账号: 
密码: 
完整的请求url: https://HOST/apiAddress
示例:
https://dyjswscl.sewage.huitucloud.cn:8088/api/sewage/waterQuality/wsWaterQualityData/getWsWaterQualityDataVoPage
2.站点数据接口列表
2.1站点数据
基础数据，极少变化，采用Excel方式提供

 请求数据类型
application/json
响应数据类型
application/json1
请求参数
参数名称	参数说明	请求类型	必须	数据类型	格式
startTime	开始时间	body	false	string	yyyy-MM-dd hh:mm:ss
endTime	结束时间	body	false	string	yyyy-MM-dd hh:mm:ss
current	页码	body	false	Integer	
pageSize	每页数量	body	false	Integer	
stationId	站点ID	body	false	Long	
响应状态
状态码	说明	schema
0	OK	ResponseResult«T»
201	Created	
401	Unauthorized	
403	Forbidden	
404	Not Found	
 
响应参数
参数名称	参数说明	类型	schema
code	返回状态码	integer(int64)	integer(int64)
data	返回数据	string	
msg	返回提示信息	string	
响应示例
{
	"code": 0,
	"data": "",
	"msg": "请求成功"
}
2.3站点水电量日数据
接口地址
POST  /api/sewage/statistics/wsStationProcessDay/page
接口描述
站点水电量日数据分页查询，每月10号查询上月数据。
请求数据类型
application/json
响应数据类型
application/json
请求参数
参数名称	参数说明	请求类型	必须	数据类型	格式
startTime	开始时间	body	false	string	yyyy-MM-dd
endTime	结束时间	body	false	string	yyyy-MM-dd
current	页码	body	false	Integer	
pageSize	每页数量	body	false	Integer	
stationId	站点ID	body	false	Long	
stationName	站点名称	body	false	string	
响应状态
状态码	说明	schema
0	OK	ResponseResult«T»
201	Created	
401	Unauthorized	
403	Forbidden	
404	Not Found	
 
响应参数
参数名称	参数说明	类型	schema
code	返回状态码	integer(int64)	integer(int64)
data	返回数据	string	
msg	返回提示信息	string	
响应示例
{
    "code": "0",
    "msg": "操作成功",
    "data": {
        "records": [
            {
                "processDayDataId": "1",
                "stationId": "1562690958710337538",
               "stationName": "忠辛台村污水处理站",
                "regionCode": "120114122",
                "regionName": "大黄堡镇",
                "processingCapacity": "10",
                "electricityConsumption": "10",
                "powerConsumption": "1",
                "reportTime": "2025-07-01"
            }
        ],
        "total": "1",
        "size": "10",
        "current": "1",
        "orders": [],
        "optimizeCountSql": true,
        "searchCount": true,
        "countId": "",
        "maxLimit": "",
        "pages": "1"
    }
}
字段说明：
参数名称	参数说明	数据类型	格式
processDayDataId	主键	Long	
stationId	站点ID	Long	
stationName	站点名称	String	
regionCode	区域编码	string	
regionName	区域名称	string	
processingCapacity	处理量（m³）	Decimal（10,2）	
electricityConsumption	用电量(kW·h)	Decimal（10,2）	
powerConsumption	吨水电耗量(kW·h)/m³	Decimal（10,2）	
reportTime	日期	string	yyyy-MM-dd


3.登录服务接口列表
3.1登录管理
3.1.1获取公钥
接口地址
POST  /api/login/getPubKey
接口描述
请求数据类型
application/json
响应数据类型
application/json
请求参数
 
参数名称	参数说明	请求类型	必须	数据类型	schema
暂无
响应状态
 
状态码	说明	schema
0	OK	返回结果«string»
201	Created	
401	Unauthorized	
403	Forbidden	
404	Not Found	
 
响应参数
参数名称	参数说明	类型	schema
code	返回状态码	integer(int64)	integer(int64)
data	返回数据	string	
msg	返回提示信息	string	
响应示例
{
	"code": 0,
	"data": "pubKey",
	"msg": "请求成功"
}
3.1.2获取登录token
接口地址
POST  /api/login/login/getToken
接口描述
请求数据类型
application/json
响应数据类型
application/json
请求示例
{
  "loginType": "account",
  "password": "000000",
  "username": "admin"
}
请求参数
 
参数名称	参数说明	请求类型	必须	数据类型	schema
loginForm	loginForm	body	true	登录信息表单	登录信息表单
 loginType	登录方式：账户		false	string	
 password	密码		false	string	
 username	用户名		false	string	
响应状态
 
状态码	说明	schema
0	OK	返回结果«string»
201	Created	
401	Unauthorized	
403	Forbidden	
404	Not Found	
 注意：
获取到公钥后，使用rsa 的加密方式获取加密后的密码！
密码加密格式：密码+@TIME@+当前时间
示例：123456@TIME@2023-03-30 10:55:45
响应参数
参数名称	参数说明	类型	schema
code	返回状态码	integer(int64)	integer(int64)
data	返回数据	string	
msg	返回提示信息	string	
响应示例
{
	"code": 0,
	"data": "Token",
	"msg": "请求成功"
}