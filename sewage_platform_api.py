#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农村生活污水智慧运维管控平台接口调用程序
作者: AI Assistant
创建时间: 2025-01-XX
"""

import requests
import json
import os
import csv
import sqlite3
from datetime import datetime, timedelta
import time
import logging
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import base64
from typing import Optional, Dict, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sewage_api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SewagePlatformAPI:
    """污水处理平台API客户端"""
    
    def __init__(self, config_file='config.json'):
        """初始化API客户端"""
        self.base_url = "https://dyjswscl.sewage.huitucloud.cn:8088"
        self.username = ""
        self.password = ""
        self.token = None
        self.session = requests.Session()
        self.session.timeout = 30
        
        # 加载配置
        self.load_config(config_file)
        
        # 初始化数据库
        self.init_database()
    
    def load_config(self, config_file: str):
        """加载配置文件"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.username = config.get('username', '')
                    self.password = config.get('password', '')
                    self.base_url = config.get('base_url', self.base_url)
                logger.info("配置文件加载成功")
            else:
                # 创建默认配置文件
                self.create_default_config(config_file)
                logger.warning(f"配置文件不存在，已创建默认配置: {config_file}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def create_default_config(self, config_file: str):
        """创建默认配置文件"""
        default_config = {
            "username": "",
            "password": "",
            "base_url": "https://dyjswscl.sewage.huitucloud.cn:8088",
            "data_retention_days": 365
        }
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"创建配置文件失败: {e}")
    
    def init_database(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect('sewage_data.db')
            cursor = conn.cursor()
            
            # 创建水电量数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS water_electric_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    process_day_data_id TEXT,
                    station_id TEXT,
                    station_name TEXT,
                    region_code TEXT,
                    region_name TEXT,
                    processing_capacity REAL,
                    electricity_consumption REAL,
                    power_consumption REAL,
                    report_time TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(process_day_data_id, report_time)
                )
            ''')
            
            # 创建水质数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS water_quality_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    station_id TEXT,
                    station_name TEXT,
                    test_time TEXT,
                    data_content TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建API调用日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_call_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_name TEXT,
                    call_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT,
                    message TEXT,
                    data_count INTEGER
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def get_public_key(self) -> Optional[str]:
        """获取公钥"""
        url = f"{self.base_url}/api/login/getPubKey"
        try:
            response = self.session.post(url)
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') == 0:
                logger.info("获取公钥成功")
                return result.get('data')
            else:
                logger.error(f"获取公钥失败: {result.get('msg')}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"获取公钥网络异常: {e}")
            return None
        except Exception as e:
            logger.error(f"获取公钥异常: {e}")
            return None
    
    def encrypt_password(self, password: str, public_key: str) -> Optional[str]:
        """RSA加密密码"""
        try:
            # 格式：密码+@TIME@+当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            password_with_time = f"{password}@TIME@{current_time}"
            
            # 处理公钥格式
            if not public_key.startswith('-----BEGIN'):
                public_key = f"-----BEGIN PUBLIC KEY-----\n{public_key}\n-----END PUBLIC KEY-----"
            
            # RSA加密
            rsa_key = RSA.importKey(public_key)
            cipher = PKCS1_v1_5.new(rsa_key)
            encrypted = cipher.encrypt(password_with_time.encode('utf-8'))
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"密码加密失败: {e}")
            return None
    
    def login(self) -> bool:
        """登录获取Token"""
        try:
            # 1. 获取公钥
            public_key = self.get_public_key()
            if not public_key:
                return False
            
            # 2. 加密密码
            encrypted_password = self.encrypt_password(self.password, public_key)
            if not encrypted_password:
                return False
            
            # 3. 登录
            url = f"{self.base_url}/api/login/login/getToken"
            login_data = {
                "loginType": "account",
                "username": self.username,
                "password": encrypted_password
            }
            
            response = self.session.post(url, json=login_data)
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') == 0:
                self.token = result.get('data')
                # 设置请求头
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                logger.info("登录成功")
                self.log_api_call("login", "成功", "登录成功", 0)
                return True
            else:
                logger.error(f"登录失败: {result.get('msg')}")
                self.log_api_call("login", "失败", result.get('msg'), 0)
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"登录网络异常: {e}")
            self.log_api_call("login", "异常", str(e), 0)
            return False
        except Exception as e:
            logger.error(f"登录异常: {e}")
            self.log_api_call("login", "异常", str(e), 0)
            return False
    
    def log_api_call(self, api_name: str, status: str, message: str, data_count: int):
        """记录API调用日志"""
        try:
            conn = sqlite3.connect('sewage_data.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO api_call_log (api_name, status, message, data_count)
                VALUES (?, ?, ?, ?)
            ''', (api_name, status, message, data_count))
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"记录API调用日志失败: {e}")

    def get_station_water_electric_data(self, start_date: str = None, end_date: str = None,
                                      station_id: str = None) -> Optional[Dict]:
        """获取站点水电量日数据"""
        url = f"{self.base_url}/api/sewage/statistics/wsStationProcessDay/page"

        # 默认获取昨天的数据
        if not start_date:
            yesterday = datetime.now() - timedelta(days=1)
            start_date = yesterday.strftime("%Y-%m-%d")
        if not end_date:
            end_date = start_date

        params = {
            "startTime": start_date,
            "endTime": end_date,
            "current": 1,
            "pageSize": 100
        }

        if station_id:
            params["stationId"] = station_id

        try:
            response = self.session.post(url, json=params)
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 0:
                data = result.get('data', {})
                records = data.get('records', [])
                logger.info(f"获取水电量数据成功，共 {len(records)} 条记录")
                self.log_api_call("water_electric_data", "成功", f"获取 {len(records)} 条记录", len(records))

                # 保存到数据库
                self.save_water_electric_data(records)
                return data
            else:
                logger.error(f"获取水电量数据失败: {result.get('msg')}")
                self.log_api_call("water_electric_data", "失败", result.get('msg'), 0)
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"获取水电量数据网络异常: {e}")
            self.log_api_call("water_electric_data", "异常", str(e), 0)
            return None
        except Exception as e:
            logger.error(f"获取水电量数据异常: {e}")
            self.log_api_call("water_electric_data", "异常", str(e), 0)
            return None

    def get_water_quality_data(self, start_time: str = None, end_time: str = None,
                             station_id: str = None) -> Optional[Dict]:
        """获取水质数据"""
        url = f"{self.base_url}/api/sewage/waterQuality/wsWaterQualityData/getWsWaterQualityDataVoPage"

        # 默认获取昨天的数据
        if not start_time:
            yesterday = datetime.now() - timedelta(days=1)
            start_time = yesterday.strftime("%Y-%m-%d 00:00:00")
            end_time = yesterday.strftime("%Y-%m-%d 23:59:59")

        params = {
            "startTime": start_time,
            "endTime": end_time,
            "current": 1,
            "pageSize": 100
        }

        if station_id:
            params["stationId"] = station_id

        try:
            response = self.session.post(url, json=params)
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 0:
                data = result.get('data', {})
                records = data.get('records', [])
                logger.info(f"获取水质数据成功，共 {len(records)} 条记录")
                self.log_api_call("water_quality_data", "成功", f"获取 {len(records)} 条记录", len(records))

                # 保存到数据库
                self.save_water_quality_data(records)
                return data
            else:
                logger.error(f"获取水质数据失败: {result.get('msg')}")
                self.log_api_call("water_quality_data", "失败", result.get('msg'), 0)
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"获取水质数据网络异常: {e}")
            self.log_api_call("water_quality_data", "异常", str(e), 0)
            return None
        except Exception as e:
            logger.error(f"获取水质数据异常: {e}")
            self.log_api_call("water_quality_data", "异常", str(e), 0)
            return None

    def save_water_electric_data(self, records: List[Dict]):
        """保存水电量数据到数据库"""
        try:
            conn = sqlite3.connect('sewage_data.db')
            cursor = conn.cursor()

            for record in records:
                cursor.execute('''
                    INSERT OR REPLACE INTO water_electric_data
                    (process_day_data_id, station_id, station_name, region_code, region_name,
                     processing_capacity, electricity_consumption, power_consumption, report_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record.get('processDayDataId'),
                    record.get('stationId'),
                    record.get('stationName'),
                    record.get('regionCode'),
                    record.get('regionName'),
                    record.get('processingCapacity'),
                    record.get('electricityConsumption'),
                    record.get('powerConsumption'),
                    record.get('reportTime')
                ))

            conn.commit()
            conn.close()
            logger.info(f"保存 {len(records)} 条水电量数据到数据库")
        except Exception as e:
            logger.error(f"保存水电量数据失败: {e}")

    def save_water_quality_data(self, records: List[Dict]):
        """保存水质数据到数据库"""
        try:
            conn = sqlite3.connect('sewage_data.db')
            cursor = conn.cursor()

            for record in records:
                cursor.execute('''
                    INSERT INTO water_quality_data
                    (station_id, station_name, test_time, data_content)
                    VALUES (?, ?, ?, ?)
                ''', (
                    record.get('stationId'),
                    record.get('stationName'),
                    record.get('testTime'),
                    json.dumps(record, ensure_ascii=False)
                ))

            conn.commit()
            conn.close()
            logger.info(f"保存 {len(records)} 条水质数据到数据库")
        except Exception as e:
            logger.error(f"保存水质数据失败: {e}")

    def export_data_to_csv(self, table_name: str, output_file: str, start_date: str = None, end_date: str = None):
        """导出数据到CSV文件"""
        try:
            conn = sqlite3.connect('sewage_data.db')

            if table_name == 'water_electric_data':
                query = '''
                    SELECT * FROM water_electric_data
                    WHERE report_time BETWEEN ? AND ?
                    ORDER BY report_time DESC
                '''
            elif table_name == 'water_quality_data':
                query = '''
                    SELECT * FROM water_quality_data
                    WHERE test_time BETWEEN ? AND ?
                    ORDER BY test_time DESC
                '''
            else:
                logger.error(f"不支持的表名: {table_name}")
                return False

            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            if not end_date:
                end_date = datetime.now().strftime("%Y-%m-%d")

            cursor = conn.cursor()
            cursor.execute(query, (start_date, end_date))
            rows = cursor.fetchall()

            if rows:
                # 获取列名
                column_names = [description[0] for description in cursor.description]

                with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(column_names)
                    writer.writerows(rows)

                logger.info(f"导出 {len(rows)} 条记录到 {output_file}")
                return True
            else:
                logger.warning("没有找到符合条件的数据")
                return False

            conn.close()
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return False
