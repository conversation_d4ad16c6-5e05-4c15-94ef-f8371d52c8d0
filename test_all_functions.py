#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有功能的脚本
"""

import sys
import os
from datetime import datetime

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    try:
        import requests
        import sqlite3
        from Crypto.PublicKey import RSA
        from Crypto.Cipher import PKCS1_v1_5
        import schedule
        from sewage_platform_api import SewagePlatformAPI
        from main import daily_data_collection, historical_data_collection, export_data, test_connection
        from scheduler import scheduled_daily_collection
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_database():
    """测试数据库功能"""
    print("\n🔍 测试数据库功能...")
    try:
        from sewage_platform_api import SewagePlatformAPI
        api = SewagePlatformAPI()
        
        # 检查数据库表
        import sqlite3
        conn = sqlite3.connect('sewage_data.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()
        
        expected_tables = ['water_electric_data', 'water_quality_data', 'api_call_log']
        table_names = [table[0] for table in tables]
        
        for expected_table in expected_tables:
            if expected_table in table_names:
                print(f"✅ 表 {expected_table} 存在")
            else:
                print(f"❌ 表 {expected_table} 不存在")
                return False
        
        print("✅ 数据库功能正常")
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    try:
        import json
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['username', 'password', 'base_url']
        for key in required_keys:
            if key in config:
                print(f"✅ 配置项 {key} 存在")
            else:
                print(f"❌ 配置项 {key} 缺失")
                return False
        
        if config['username'] and config['password']:
            print("✅ 用户名和密码已配置")
        else:
            print("⚠️  用户名或密码为空（这是正常的，需要用户手动配置）")
        
        print("✅ 配置文件格式正确")
        return True
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🔍 测试API连接...")
    try:
        from sewage_platform_api import SewagePlatformAPI
        api = SewagePlatformAPI()
        
        # 测试获取公钥
        public_key = api.get_public_key()
        if public_key:
            print("✅ 公钥获取成功")
            print(f"   公钥长度: {len(public_key)} 字符")
            return True
        else:
            print("❌ 公钥获取失败")
            return False
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def test_encryption():
    """测试加密功能"""
    print("\n🔍 测试RSA加密功能...")
    try:
        from sewage_platform_api import SewagePlatformAPI
        api = SewagePlatformAPI()
        
        # 获取公钥
        public_key = api.get_public_key()
        if not public_key:
            print("❌ 无法获取公钥，跳过加密测试")
            return False
        
        # 测试加密
        test_password = "test123"
        encrypted = api.encrypt_password(test_password, public_key)
        if encrypted:
            print("✅ RSA加密功能正常")
            print(f"   加密后长度: {len(encrypted)} 字符")
            return True
        else:
            print("❌ RSA加密失败")
            return False
    except Exception as e:
        print(f"❌ 加密测试失败: {e}")
        return False

def test_command_line():
    """测试命令行功能"""
    print("\n🔍 测试命令行功能...")
    try:
        import subprocess
        
        # 测试帮助信息
        result = subprocess.run([sys.executable, 'main.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and '污水处理平台数据采集工具' in result.stdout:
            print("✅ 命令行帮助功能正常")
        else:
            print("❌ 命令行帮助功能异常")
            return False
        
        print("✅ 命令行功能正常")
        return True
    except Exception as e:
        print(f"❌ 命令行测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始全面功能测试...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据库功能", test_database),
        ("配置文件", test_config),
        ("API连接", test_api_connection),
        ("RSA加密", test_encryption),
        ("命令行功能", test_command_line),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！代码运行正常！")
        print("\n📝 使用说明:")
        print("1. 编辑 config.json 文件，填入正确的用户名和密码")
        print("2. 运行 python main.py --mode test 测试连接")
        print("3. 运行 python main.py --mode daily 执行数据采集")
        return True
    else:
        print("⚠️  部分功能测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
