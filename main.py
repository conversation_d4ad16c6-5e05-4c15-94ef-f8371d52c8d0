#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程序 - 污水处理平台数据采集
"""

import argparse
import sys
from datetime import datetime, timedelta
from sewage_platform_api import SewagePlatformAPI, logger

def daily_data_collection():
    """每日数据采集任务"""
    logger.info("开始每日数据采集任务")
    
    api = SewagePlatformAPI()
    
    # 检查配置
    if not api.username or not api.password:
        logger.error("请先在config.json中配置用户名和密码")
        return False
    
    # 登录
    if not api.login():
        logger.error("登录失败，退出数据采集")
        return False
    
    success_count = 0
    
    # 获取昨天的水电量数据
    logger.info("正在获取水电量数据...")
    water_electric_data = api.get_station_water_electric_data()
    if water_electric_data:
        success_count += 1
        
    # 获取昨天的水质数据
    logger.info("正在获取水质数据...")
    water_quality_data = api.get_water_quality_data()
    if water_quality_data:
        success_count += 1
    
    logger.info(f"每日数据采集完成，成功获取 {success_count}/2 个接口数据")
    return success_count > 0

def historical_data_collection(start_date: str, end_date: str):
    """历史数据采集"""
    logger.info(f"开始历史数据采集: {start_date} 到 {end_date}")
    
    api = SewagePlatformAPI()
    
    # 检查配置
    if not api.username or not api.password:
        logger.error("请先在config.json中配置用户名和密码")
        return False
    
    # 登录
    if not api.login():
        logger.error("登录失败，退出数据采集")
        return False
    
    # 按天循环获取数据
    current_date = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
    
    total_days = (end_date_obj - current_date).days + 1
    success_days = 0
    
    while current_date <= end_date_obj:
        date_str = current_date.strftime("%Y-%m-%d")
        logger.info(f"正在获取 {date_str} 的数据...")
        
        day_success = 0
        
        # 获取水电量数据
        water_electric_data = api.get_station_water_electric_data(date_str, date_str)
        if water_electric_data:
            day_success += 1
            
        # 获取水质数据
        start_time = f"{date_str} 00:00:00"
        end_time = f"{date_str} 23:59:59"
        water_quality_data = api.get_water_quality_data(start_time, end_time)
        if water_quality_data:
            day_success += 1
        
        if day_success > 0:
            success_days += 1
        
        current_date += timedelta(days=1)
        
        # 避免请求过于频繁
        import time
        time.sleep(1)
    
    logger.info(f"历史数据采集完成，成功获取 {success_days}/{total_days} 天的数据")
    return success_days > 0

def export_data(table_name: str, output_file: str, start_date: str = None, end_date: str = None):
    """导出数据"""
    logger.info(f"开始导出数据: {table_name} -> {output_file}")
    
    api = SewagePlatformAPI()
    success = api.export_data_to_csv(table_name, output_file, start_date, end_date)
    
    if success:
        logger.info("数据导出成功")
    else:
        logger.error("数据导出失败")
    
    return success

def test_connection():
    """测试连接"""
    logger.info("开始测试连接...")
    
    api = SewagePlatformAPI()
    
    # 检查配置
    if not api.username or not api.password:
        logger.error("请先在config.json中配置用户名和密码")
        return False
    
    # 测试登录
    if api.login():
        logger.info("连接测试成功")
        return True
    else:
        logger.error("连接测试失败")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='污水处理平台数据采集工具')
    parser.add_argument('--mode', choices=['daily', 'historical', 'export', 'test'], 
                       default='daily', help='运行模式')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--table', choices=['water_electric_data', 'water_quality_data'], 
                       help='要导出的表名')
    parser.add_argument('--output', help='输出文件名')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'daily':
            # 每日数据采集
            success = daily_data_collection()
            sys.exit(0 if success else 1)
            
        elif args.mode == 'historical':
            # 历史数据采集
            if not args.start_date or not args.end_date:
                logger.error("历史数据采集需要指定开始和结束日期")
                sys.exit(1)
            success = historical_data_collection(args.start_date, args.end_date)
            sys.exit(0 if success else 1)
            
        elif args.mode == 'export':
            # 导出数据
            if not args.table or not args.output:
                logger.error("导出数据需要指定表名和输出文件")
                sys.exit(1)
            success = export_data(args.table, args.output, args.start_date, args.end_date)
            sys.exit(0 if success else 1)
            
        elif args.mode == 'test':
            # 测试连接
            success = test_connection()
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
